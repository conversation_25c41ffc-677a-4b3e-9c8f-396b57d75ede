/**
 * Smart Factory WMS - Vue Router Configuration
 * 
 * This file contains all route definitions and navigation guards
 * for the Smart Factory WMS frontend application.
 */

import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store'
import { authGuard, guestGuard, roleGuard } from './guards'

// Lazy-loaded components for better performance
const Dashboard = () => import('@/views/Dashboard.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const ForgotPassword = () => import('@/views/auth/ForgotPassword.vue')
const ResetPassword = () => import('@/views/auth/ResetPassword.vue')

// Inventory Management
const InventoryList = () => import('@/views/inventory/InventoryList.vue')
const InventoryDetail = () => import('@/views/inventory/InventoryDetail.vue')
const InventoryAdjustment = () => import('@/views/inventory/InventoryAdjustment.vue')

// Receiving Management
const ReceivingList = () => import('@/views/receiving/ReceivingList.vue')
const ReceivingDetail = () => import('@/views/receiving/ReceivingDetail.vue')
const ReceivingCreate = () => import('@/views/receiving/ReceivingCreate.vue')

// Shipment Management
const ShipmentList = () => import('@/views/shipment/ShipmentList.vue')
const ShipmentDetail = () => import('@/views/shipment/ShipmentDetail.vue')
const ShipmentCreate = () => import('@/views/shipment/ShipmentCreate.vue')

// Production Management
const ProductionList = () => import('@/views/production/ProductionList.vue')
const ProductionDetail = () => import('@/views/production/ProductionDetail.vue')
const ProductionCreate = () => import('@/views/production/ProductionCreate.vue')

// Reports
const Reports = () => import('@/views/reports/Reports.vue')
const InventoryReport = () => import('@/views/reports/InventoryReport.vue')
const ProductionReport = () => import('@/views/reports/ProductionReport.vue')

// User Management
const UserList = () => import('@/views/users/UserList.vue')
const UserDetail = () => import('@/views/users/UserDetail.vue')
const UserCreate = () => import('@/views/users/UserCreate.vue')
const Profile = () => import('@/views/users/Profile.vue')

// Settings
const Settings = () => import('@/views/settings/Settings.vue')

// Error Pages
const NotFound = () => import('@/views/errors/NotFound.vue')
const Unauthorized = () => import('@/views/errors/Unauthorized.vue')
const ServerError = () => import('@/views/errors/ServerError.vue')

const routes = [
  // Root redirect
  {
    path: '/',
    redirect: '/dashboard'
  },
  
  // Authentication routes (guest only)
  {
    path: '/login',
    name: 'Login',
    component: Login,
    beforeEnter: guestGuard,
    meta: {
      title: 'auth.login',
      layout: 'auth'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    beforeEnter: guestGuard,
    meta: {
      title: 'auth.register',
      layout: 'auth'
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    beforeEnter: guestGuard,
    meta: {
      title: 'auth.forgot_password',
      layout: 'auth'
    }
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: ResetPassword,
    beforeEnter: guestGuard,
    meta: {
      title: 'auth.reset_password',
      layout: 'auth'
    }
  },
  
  // Protected routes (authenticated users only)
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.dashboard',
      requiresAuth: true
    }
  },
  
  // Inventory Management
  {
    path: '/inventory',
    name: 'InventoryList',
    component: InventoryList,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.inventory',
      requiresAuth: true,
      permissions: ['inventory.view']
    }
  },
  {
    path: '/inventory/:id',
    name: 'InventoryDetail',
    component: InventoryDetail,
    beforeEnter: authGuard,
    meta: {
      title: 'inventory.detail',
      requiresAuth: true,
      permissions: ['inventory.view']
    }
  },
  {
    path: '/inventory/adjustment',
    name: 'InventoryAdjustment',
    component: InventoryAdjustment,
    beforeEnter: authGuard,
    meta: {
      title: 'inventory.adjustment',
      requiresAuth: true,
      permissions: ['inventory.adjust']
    }
  },
  
  // Receiving Management
  {
    path: '/receiving',
    name: 'ReceivingList',
    component: ReceivingList,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.receiving',
      requiresAuth: true,
      permissions: ['receiving.view']
    }
  },
  {
    path: '/receiving/create',
    name: 'ReceivingCreate',
    component: ReceivingCreate,
    beforeEnter: authGuard,
    meta: {
      title: 'receiving.create',
      requiresAuth: true,
      permissions: ['receiving.create']
    }
  },
  {
    path: '/receiving/:id',
    name: 'ReceivingDetail',
    component: ReceivingDetail,
    beforeEnter: authGuard,
    meta: {
      title: 'receiving.detail',
      requiresAuth: true,
      permissions: ['receiving.view']
    }
  },
  
  // Shipment Management
  {
    path: '/shipment',
    name: 'ShipmentList',
    component: ShipmentList,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.shipment',
      requiresAuth: true,
      permissions: ['shipment.view']
    }
  },
  {
    path: '/shipment/create',
    name: 'ShipmentCreate',
    component: ShipmentCreate,
    beforeEnter: authGuard,
    meta: {
      title: 'shipment.create',
      requiresAuth: true,
      permissions: ['shipment.create']
    }
  },
  {
    path: '/shipment/:id',
    name: 'ShipmentDetail',
    component: ShipmentDetail,
    beforeEnter: authGuard,
    meta: {
      title: 'shipment.detail',
      requiresAuth: true,
      permissions: ['shipment.view']
    }
  },
  
  // Production Management
  {
    path: '/production',
    name: 'ProductionList',
    component: ProductionList,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.production',
      requiresAuth: true,
      permissions: ['production.view']
    }
  },
  {
    path: '/production/create',
    name: 'ProductionCreate',
    component: ProductionCreate,
    beforeEnter: authGuard,
    meta: {
      title: 'production.create',
      requiresAuth: true,
      permissions: ['production.create']
    }
  },
  {
    path: '/production/:id',
    name: 'ProductionDetail',
    component: ProductionDetail,
    beforeEnter: authGuard,
    meta: {
      title: 'production.detail',
      requiresAuth: true,
      permissions: ['production.view']
    }
  },
  
  // Reports
  {
    path: '/reports',
    name: 'Reports',
    component: Reports,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.reports',
      requiresAuth: true,
      permissions: ['reports.view']
    }
  },
  {
    path: '/reports/inventory',
    name: 'InventoryReport',
    component: InventoryReport,
    beforeEnter: authGuard,
    meta: {
      title: 'reports.inventory',
      requiresAuth: true,
      permissions: ['reports.view']
    }
  },
  {
    path: '/reports/production',
    name: 'ProductionReport',
    component: ProductionReport,
    beforeEnter: authGuard,
    meta: {
      title: 'reports.production',
      requiresAuth: true,
      permissions: ['reports.view']
    }
  },
  
  // User Management
  {
    path: '/users',
    name: 'UserList',
    component: UserList,
    beforeEnter: [authGuard, roleGuard(['admin', 'manager'])],
    meta: {
      title: 'menu.users',
      requiresAuth: true,
      permissions: ['users.view'],
      roles: ['admin', 'manager']
    }
  },
  {
    path: '/users/create',
    name: 'UserCreate',
    component: UserCreate,
    beforeEnter: [authGuard, roleGuard(['admin'])],
    meta: {
      title: 'users.create',
      requiresAuth: true,
      permissions: ['users.create'],
      roles: ['admin']
    }
  },
  {
    path: '/users/:id',
    name: 'UserDetail',
    component: UserDetail,
    beforeEnter: authGuard,
    meta: {
      title: 'users.detail',
      requiresAuth: true,
      permissions: ['users.view']
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.profile',
      requiresAuth: true
    }
  },
  
  // Settings
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    beforeEnter: authGuard,
    meta: {
      title: 'menu.settings',
      requiresAuth: true
    }
  },
  
  // Error Pages
  {
    path: '/unauthorized',
    name: 'Unauthorized',
    component: Unauthorized,
    meta: {
      title: 'errors.unauthorized'
    }
  },
  {
    path: '/server-error',
    name: 'ServerError',
    component: ServerError,
    meta: {
      title: 'errors.server_error'
    }
  },
  
  // 404 - Must be last
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: 'errors.not_found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Global navigation guards
router.beforeEach(async (to, from, next) => {
  // Start loading indicator
  store.commit('ui/SET_LOADING', true)
  
  // Check if user is authenticated for protected routes
  if (to.meta.requiresAuth && !store.getters['auth/isAuthenticated']) {
    next('/login')
    return
  }
  
  // Check permissions
  if (to.meta.permissions && !store.getters['auth/hasPermissions'](to.meta.permissions)) {
    next('/unauthorized')
    return
  }
  
  next()
})

router.afterEach(() => {
  // Stop loading indicator
  store.commit('ui/SET_LOADING', false)
})

export default router
